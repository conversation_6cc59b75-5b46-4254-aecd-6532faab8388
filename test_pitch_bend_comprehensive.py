#!/usr/bin/env python3
"""
Comprehensive test script to verify the pitch bending system works correctly 
and doesn't get stuck when processing the same MIDI multiple times.
"""

import sys
import os
import numpy as np
import importlib.util

# Add the current directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def import_midi_converter():
    """Import the main MIDI converter module"""
    spec = importlib.util.spec_from_file_location(
        "midi_converter", 
        "midi-to-audio-with-correct-bpm - Copy (3) - Copy.py"
    )
    midi_converter = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(midi_converter)
    return midi_converter

def test_pitch_bend_calculations():
    """Test pitch bend calculations match midi loader.py exactly"""
    print("Testing pitch bend calculations...")
    
    test_cases = [
        (8192, 2.0, 0.0),      # Center position
        (16383, 2.0, 2.0),     # Max up
        (0, 2.0, -2.0),        # <PERSON> down
        (12287, 2.0, 1.0),     # Half up
        (16383, 12.0, 12.0),   # <PERSON> up with 12 semitone range
        (4096, 2.0, -1.0),     # Half down
    ]
    
    for bend_value, range_semitones, expected_semitones in test_cases:
        # Use exact same calculation as midi loader.py
        semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
        print(f"✓ Bend value {bend_value} with range {range_semitones} = {semitone_offset:.3f} semitones")
        assert abs(semitone_offset - expected_semitones) < 0.01, f"Expected {expected_semitones}, got {semitone_offset}"
    
    # Test pitch factor calculations
    semitone_tests = [-12, -2, 0, 2, 12]
    expected_factors = [0.5, 0.891, 1.0, 1.122, 2.0]
    
    for semitones, expected_factor in zip(semitone_tests, expected_factors):
        pitch_factor = 2 ** (semitones / 12.0)
        print(f"✓ {semitones} semitones = pitch factor {pitch_factor:.3f}")
        assert abs(pitch_factor - expected_factor) < 0.01, f"Expected {expected_factor}, got {pitch_factor}"
    
    return True

def test_midi_event_creation():
    """Test MIDI event creation"""
    print("\nTesting MIDI event creation...")
    
    try:
        midi_converter = import_midi_converter()
        
        # Test pitch bend event
        event = midi_converter.MIDIEvent(1000, 'pitch_bend', 0, pitch_bend=12287)
        assert event.time == 1000
        assert event.event_type == 'pitch_bend'
        assert event.channel == 0
        assert event.pitch_bend == 12287
        print("✓ Pitch bend event creation works")
        
        # Test CC event
        cc_event = midi_converter.MIDIEvent(2000, 'control_change', 1, 6, 12)  # RPN Data Entry MSB
        assert cc_event.time == 2000
        assert cc_event.event_type == 'control_change'
        assert cc_event.channel == 1
        assert cc_event.param1 == 6
        assert cc_event.param2 == 12
        print("✓ CC event creation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing MIDI event creation: {e}")
        return False

def test_pitch_bend_map_consistency():
    """Test that pitch bend maps are built consistently and don't accumulate state"""
    print("\nTesting pitch bend map consistency...")
    
    try:
        midi_converter = import_midi_converter()
        
        # Mock tempo changes and track parameters
        tempo_changes = [(0, 500000)]  # 120 BPM
        ticks_per_beat = 480
        
        # Create mock track data with pitch bend events
        # We'll simulate multiple processing runs to test for stuck events
        
        results = []
        
        for iteration in range(5):  # Test 5 iterations
            print(f"  Iteration {iteration + 1}:")
            
            # Create mock events that simulate a typical pitch bend sequence
            mock_events = [
                midi_converter.MIDIEvent(0, 'pitch_bend', 0, pitch_bend=8192),      # Center at start
                midi_converter.MIDIEvent(240, 'pitch_bend', 0, pitch_bend=10240),   # Slight bend up
                midi_converter.MIDIEvent(480, 'pitch_bend', 0, pitch_bend=12287),   # More bend up
                midi_converter.MIDIEvent(720, 'pitch_bend', 0, pitch_bend=8192),    # Back to center
                midi_converter.MIDIEvent(960, 'pitch_bend', 0, pitch_bend=4096),    # Bend down
                midi_converter.MIDIEvent(1200, 'pitch_bend', 0, pitch_bend=8192),   # Back to center
            ]
            
            # Simulate the pitch bend map building process
            pitch_bend_map = {ch: [(0.0, 0.0)] for ch in range(16)}  # Initialize with center
            pitch_bend_range = {ch: 2.0 for ch in range(16)}
            
            for event in mock_events:
                if event.event_type == 'pitch_bend':
                    bend_value = max(0, min(16383, event.pitch_bend))  # Clamp to valid range
                    range_semitones = pitch_bend_range.get(event.channel, 2.0)
                    semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
                    # Convert ticks to time (simplified)
                    event_time = event.time / ticks_per_beat * (500000 / 1000000)
                    pitch_bend_map[event.channel].append((event_time, semitone_offset))
            
            # Store results for comparison
            channel_0_bends = pitch_bend_map[0]
            results.append(channel_0_bends)
            
            print(f"    Channel 0 bends: {len(channel_0_bends)} events")
            for i, (time, bend) in enumerate(channel_0_bends):
                print(f"      {i}: time={time:.3f}s, bend={bend:.3f} semitones")
        
        # Verify all iterations produced identical results
        print("\n  Comparing results across iterations...")
        first_result = results[0]
        
        for i, result in enumerate(results[1:], 2):
            assert len(result) == len(first_result), f"Iteration {i} has different number of events"
            
            for j, ((exp_time, exp_bend), (act_time, act_bend)) in enumerate(zip(first_result, result)):
                assert abs(act_time - exp_time) < 0.001, f"Time mismatch in iteration {i}, event {j}"
                assert abs(act_bend - exp_bend) < 0.001, f"Bend mismatch in iteration {i}, event {j}"
        
        print("✓ All iterations produced identical results - no stuck events!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing pitch bend map consistency: {e}")
        return False

def test_pitch_bend_value_clamping():
    """Test that pitch bend values are properly clamped to valid range"""
    print("\nTesting pitch bend value clamping...")
    
    try:
        # Test edge cases and invalid values
        test_values = [
            (-1000, 0),      # Negative value should clamp to 0
            (0, 0),          # Minimum valid value
            (8192, 8192),    # Center value
            (16383, 16383),  # Maximum valid value
            (20000, 16383),  # Over-range value should clamp to 16383
        ]
        
        for input_val, expected_val in test_values:
            clamped_val = max(0, min(16383, input_val))
            assert clamped_val == expected_val, f"Expected {expected_val}, got {clamped_val}"
            print(f"✓ Value {input_val} clamped to {clamped_val}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing pitch bend value clamping: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Comprehensive Pitch Bend System Test ===\n")
    
    tests = [
        test_pitch_bend_calculations,
        test_midi_event_creation,
        test_pitch_bend_map_consistency,
        test_pitch_bend_value_clamping,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED\n")
            else:
                print(f"✗ {test.__name__} FAILED\n")
        except Exception as e:
            print(f"✗ {test.__name__} FAILED with exception: {e}\n")
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("The pitch bending system should work correctly and not get stuck.")
        print("You can now process the same MIDI file multiple times without issues.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the implementation for issues.")
    
    return passed == total

if __name__ == "__main__":
    main()
