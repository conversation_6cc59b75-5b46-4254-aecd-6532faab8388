#!/usr/bin/env python3
"""
Master test runner that builds Cython extensions and runs all tests.
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n=== {description} ===")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {description} succeeded")
            if result.stdout.strip():
                print("Output:")
                print(result.stdout)
            return True
        else:
            print(f"✗ {description} failed")
            if result.stderr.strip():
                print("Error:")
                print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ {description} failed with exception: {e}")
        return False

def main():
    """Main test runner"""
    print("🚀 Starting comprehensive pitch bend system test...")
    
    # Step 1: Try to build Cython extensions (optional)
    print("\n" + "="*60)
    print("STEP 1: Building Cython Extensions (Optional)")
    print("="*60)
    
    cython_success = run_command("python build_cython.py", "Cython extension build")
    if cython_success:
        print("🎉 Cython extensions built successfully - performance will be optimized!")
    else:
        print("⚠️  Cython extensions not available - will use pure Python (slower but functional)")
    
    # Step 2: Run tempo and pitch bend tests
    print("\n" + "="*60)
    print("STEP 2: Testing Tempo Changes & Pitch Bend Handling")
    print("="*60)
    
    tempo_test_success = run_command("python test_tempo_pitch_bend.py", "Tempo change and pitch bend tests")
    
    # Step 3: Run comprehensive pitch bend tests
    print("\n" + "="*60)
    print("STEP 3: Running Comprehensive Pitch Bend Tests")
    print("="*60)
    
    comprehensive_test_success = run_command("python test_pitch_bend_comprehensive.py", "Comprehensive pitch bend tests")
    
    # Summary
    print("\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    
    total_tests = 2  # tempo_test and comprehensive_test
    passed_tests = sum([tempo_test_success, comprehensive_test_success])
    
    print(f"Cython Extensions: {'✓ Available' if cython_success else '✗ Not Available (using pure Python)'}")
    print(f"Tempo & Pitch Bend Tests: {'✓ PASSED' if tempo_test_success else '✗ FAILED'}")
    print(f"Comprehensive Tests: {'✓ PASSED' if comprehensive_test_success else '✗ FAILED'}")
    print(f"\nOverall: {passed_tests}/{total_tests} test suites passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Pitch bend events should not get stuck")
        print("✅ Tempo changes are handled correctly")
        print("✅ Same MIDI can be processed multiple times safely")
        if cython_success:
            print("✅ Performance is optimized with Cython")
        print("\nThe pitch bending system is working correctly!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("There may still be issues with the pitch bend system.")
        print("Please check the test output above for details.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
