#!/usr/bin/env python3
"""
Setup script to compile Cython extensions for pitch bend processing.
Run with: python setup_cython.py build_ext --inplace
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# Define the extension
extensions = [
    Extension(
        "pitch_bend_fast",
        ["pitch_bend_fast.pyx"],
        include_dirs=[numpy.get_include()],
        extra_compile_args=["-O3", "-ffast-math"],  # Optimization flags
        extra_link_args=["-O3"],
    )
]

# Setup
setup(
    name="pitch_bend_fast",
    ext_modules=cythonize(extensions, compiler_directives={
        'language_level': 3,
        'boundscheck': False,
        'wraparound': False,
        'cdivision': True,
        'embedsignature': True,
    }),
    zip_safe=False,
)
