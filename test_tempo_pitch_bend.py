#!/usr/bin/env python3
"""
Test script to verify that pitch bend events work correctly with tempo changes
and don't get stuck when processing the same MIDI multiple times.
"""

import sys
import os
import numpy as np
import importlib.util

# Add the current directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def import_midi_converter():
    """Import the main MIDI converter module"""
    spec = importlib.util.spec_from_file_location(
        "midi_converter", 
        "midi-to-audio-with-correct-bpm - Copy (3) - Copy.py"
    )
    midi_converter = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(midi_converter)
    return midi_converter

def test_tempo_change_handling():
    """Test that tempo changes don't cause pitch bend events to get stuck"""
    print("Testing tempo change handling with pitch bends...")
    
    try:
        midi_converter = import_midi_converter()
        
        # Create tempo changes that simulate a real MIDI file
        tempo_changes = [
            (0, 500000),      # 120 BPM at start
            (960, 400000),    # 150 BPM at beat 2
            (1920, 600000),   # 100 BPM at beat 4
        ]
        ticks_per_beat = 480
        
        # Test multiple iterations to ensure no state persistence
        results = []
        
        for iteration in range(3):
            print(f"  Iteration {iteration + 1}:")
            
            # Build pitch bend map with tempo changes
            pitch_bend_map, pitch_bend_range = midi_converter.build_global_pitch_bend_map(
                b'',  # Empty track data for this test
                ticks_per_beat,
                tempo_changes
            )
            
            # Verify initial state
            for ch in range(16):
                assert len(pitch_bend_map[ch]) >= 1, f"Channel {ch} should have at least initial bend"
                assert pitch_bend_range[ch] == 2.0, f"Channel {ch} should have default range"
                
                # Check that initial bend is at time 0.0 with value 0.0
                initial_time, initial_bend = pitch_bend_map[ch][0]
                assert initial_time == 0.0, f"Initial time should be 0.0, got {initial_time}"
                assert initial_bend == 0.0, f"Initial bend should be 0.0, got {initial_bend}"
            
            results.append((pitch_bend_map, pitch_bend_range))
            print(f"    [OK] Iteration {iteration + 1} completed successfully")
        
        # Verify all iterations produced identical results
        first_map, first_range = results[0]
        for i, (test_map, test_range) in enumerate(results[1:], 2):
            for ch in range(16):
                assert len(test_map[ch]) == len(first_map[ch]), f"Iteration {i} channel {ch} length mismatch"
                assert test_range[ch] == first_range[ch], f"Iteration {i} channel {ch} range mismatch"
                
                for j, ((t1, b1), (t2, b2)) in enumerate(zip(first_map[ch], test_map[ch])):
                    assert abs(t1 - t2) < 0.001, f"Time mismatch in iteration {i}, channel {ch}, event {j}"
                    assert abs(b1 - b2) < 0.001, f"Bend mismatch in iteration {i}, channel {ch}, event {j}"
        
        print("[OK] All iterations produced identical results - no stuck events!")
        return True

    except Exception as e:
        print(f"[ERROR] Error testing tempo change handling: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pitch_bend_calculation_consistency():
    """Test that pitch bend calculations are consistent across multiple calls"""
    print("\nTesting pitch bend calculation consistency...")
    
    try:
        midi_converter = import_midi_converter()
        
        # Test values that commonly cause issues
        test_cases = [
            (0, 2.0),        # Minimum bend
            (8192, 2.0),     # Center (no bend)
            (16383, 2.0),    # Maximum bend
            (4096, 2.0),     # Quarter down
            (12288, 2.0),    # Quarter up
            (8192, 12.0),    # Center with large range
            (16383, 0.5),    # Max with small range
        ]
        
        # Test each case multiple times
        for bend_value, range_semitones in test_cases:
            results = []
            
            for iteration in range(5):
                if hasattr(midi_converter, 'CYTHON_AVAILABLE') and midi_converter.CYTHON_AVAILABLE:
                    result = midi_converter.calculate_pitch_bend_fast(bend_value, range_semitones)
                else:
                    result = ((bend_value - 8192) / 8192) * range_semitones
                results.append(result)
            
            # Verify all results are identical
            first_result = results[0]
            for i, result in enumerate(results[1:], 2):
                assert abs(result - first_result) < 1e-10, f"Inconsistent result in iteration {i}"
            
            print(f"    [OK] Bend {bend_value} with range {range_semitones} = {first_result:.6f} (consistent)")

        return True

    except Exception as e:
        print(f"[ERROR] Error testing pitch bend calculation consistency: {e}")
        return False

def test_pitch_bend_state_reset():
    """Test that pitch bend state is properly reset between different processing runs"""
    print("\nTesting pitch bend state reset...")
    
    try:
        midi_converter = import_midi_converter()
        
        # Simulate processing the same MIDI file multiple times
        # This is the scenario where users report stuck pitch bends
        
        tempo_changes = [(0, 500000)]  # Simple tempo
        ticks_per_beat = 480
        
        # Create mock events that would cause state accumulation if not handled properly
        mock_events = [
            midi_converter.MIDIEvent(0, 'pitch_bend', 0, pitch_bend=8192),      # Center
            midi_converter.MIDIEvent(240, 'pitch_bend', 0, pitch_bend=12288),   # Bend up
            midi_converter.MIDIEvent(480, 'pitch_bend', 1, pitch_bend=4096),    # Different channel bend down
            midi_converter.MIDIEvent(720, 'pitch_bend', 0, pitch_bend=8192),    # Back to center
        ]
        
        # Process multiple times and verify consistent results
        all_results = []
        
        for run in range(5):
            print(f"  Processing run {run + 1}...")
            
            # Build fresh pitch bend map
            pitch_bend_map, pitch_bend_range = midi_converter.build_global_pitch_bend_map(
                b'',  # Empty track data
                ticks_per_beat,
                tempo_changes
            )
            
            # Simulate adding the mock events
            for event in mock_events:
                if event.event_type == 'pitch_bend':
                    channel = event.channel
                    bend_value = max(0, min(16383, event.pitch_bend))
                    range_semitones = pitch_bend_range.get(channel, 2.0)
                    
                    if hasattr(midi_converter, 'CYTHON_AVAILABLE') and midi_converter.CYTHON_AVAILABLE:
                        semitone_offset = midi_converter.calculate_pitch_bend_fast(bend_value, range_semitones)
                    else:
                        semitone_offset = ((bend_value - 8192) / 8192) * range_semitones
                    
                    # Convert ticks to time
                    event_time = event.time / ticks_per_beat * (500000 / 1000000)
                    pitch_bend_map[channel].append((event_time, semitone_offset))
            
            # Store results
            all_results.append(pitch_bend_map)
            
            # Verify expected structure
            assert len(pitch_bend_map[0]) >= 3, "Channel 0 should have multiple bends"  # Initial + 2 events
            assert len(pitch_bend_map[1]) >= 2, "Channel 1 should have initial + 1 event"
            
            print(f"    Channel 0 events: {len(pitch_bend_map[0])}")
            print(f"    Channel 1 events: {len(pitch_bend_map[1])}")
        
        # Verify all runs produced identical results
        first_result = all_results[0]
        for run_idx, result in enumerate(all_results[1:], 2):
            for ch in range(16):
                assert len(result[ch]) == len(first_result[ch]), f"Run {run_idx} channel {ch} length mismatch"
                
                for event_idx, ((t1, b1), (t2, b2)) in enumerate(zip(first_result[ch], result[ch])):
                    assert abs(t1 - t2) < 0.001, f"Time mismatch in run {run_idx}, channel {ch}, event {event_idx}"
                    assert abs(b1 - b2) < 0.001, f"Bend mismatch in run {run_idx}, channel {ch}, event {event_idx}"
        
        print("[OK] All processing runs produced identical results - state is properly reset!")
        return True

    except Exception as e:
        print(f"[ERROR] Error testing pitch bend state reset: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=== Tempo Change & Pitch Bend Test Suite ===\n")
    
    tests = [
        test_tempo_change_handling,
        test_pitch_bend_calculation_consistency,
        test_pitch_bend_state_reset,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"[PASS] {test.__name__} PASSED\n")
            else:
                print(f"[FAIL] {test.__name__} FAILED\n")
        except Exception as e:
            print(f"[FAIL] {test.__name__} FAILED with exception: {e}\n")
    
    print("=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("SUCCESS: ALL TESTS PASSED!")
        print("Pitch bend events should not get stuck, even with tempo changes.")
        print("You can now process the same MIDI file multiple times safely.")
    else:
        print("FAILURE: SOME TESTS FAILED!")
        print("There may still be issues with pitch bend event handling.")
    
    return passed == total

if __name__ == "__main__":
    main()
