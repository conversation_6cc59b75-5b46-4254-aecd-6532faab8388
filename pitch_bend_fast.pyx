# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True

import numpy as np
cimport numpy as cnp
cimport cython
from libc.math cimport pow, round
from libc.stdlib cimport malloc, free

ctypedef cnp.float32_t DTYPE_t
ctypedef cnp.int32_t INT_DTYPE_t

@cython.boundscheck(False)
@cython.wraparound(False)
def resample_segment_fast(cnp.ndarray[DTYPE_t, ndim=2] segment, int output_length):
    """High-performance resampling using Cython"""
    cdef int input_length = segment.shape[0]
    cdef int channels = segment.shape[1]
    
    if input_length == 0:
        return np.zeros((output_length, channels), dtype=np.float32)
    if output_length <= 0:
        return np.zeros((1, channels), dtype=np.float32)
    
    cdef cnp.ndarray[DTYPE_t, ndim=2] result = np.zeros((output_length, channels), dtype=np.float32)
    
    cdef double ratio = <double>(input_length - 1) / <double>(output_length - 1) if output_length > 1 else 0.0
    cdef int i, ch, idx_low, idx_high
    cdef double pos, frac
    cdef DTYPE_t val_low, val_high
    
    for i in range(output_length):
        pos = i * ratio
        idx_low = <int>pos
        idx_high = idx_low + 1
        frac = pos - idx_low
        
        if idx_high >= input_length:
            idx_high = input_length - 1
            frac = 0.0
        
        for ch in range(channels):
            val_low = segment[idx_low, ch]
            val_high = segment[idx_high, ch]
            result[i, ch] = val_low + frac * (val_high - val_low)
    
    return result

@cython.boundscheck(False)
@cython.wraparound(False)
def calculate_pitch_bend_fast(int bend_value, double range_semitones):
    """Fast pitch bend calculation"""
    # Clamp bend_value to valid range
    if bend_value < 0:
        bend_value = 0
    elif bend_value > 16383:
        bend_value = 16383
    
    # Use exact same calculation as midi loader.py
    cdef double semitone_offset = ((bend_value - 8192.0) / 8192.0) * range_semitones
    return semitone_offset

@cython.boundscheck(False)
@cython.wraparound(False)
def calculate_pitch_factor_fast(double semitone_offset):
    """Fast pitch factor calculation"""
    return pow(2.0, semitone_offset / 12.0)

@cython.boundscheck(False)
@cython.wraparound(False)
def process_pitch_bend_segment_fast(
    cnp.ndarray[DTYPE_t, ndim=2] input_seg,
    int seg_output_samples,
    double bend_semitones,
    double velocity_factor,
    cnp.ndarray[DTYPE_t, ndim=2] output,
    int output_start,
    bint apply_release_fade,
    cnp.ndarray[DTYPE_t, ndim=1] release_env = None
):
    """Fast processing of a pitch bend segment"""
    
    if seg_output_samples <= 0 or input_seg.shape[0] == 0:
        return
    
    # Calculate pitch factor
    cdef double pitch_factor = pow(2.0, bend_semitones / 12.0)
    cdef int seg_input_samples = <int>(seg_output_samples * pitch_factor)
    
    # Resample the segment
    cdef cnp.ndarray[DTYPE_t, ndim=2] resampled_seg = resample_segment_fast(input_seg, seg_output_samples)
    
    # Calculate bounds
    cdef int output_end = output_start + resampled_seg.shape[0]
    cdef int output_height = output.shape[0]
    cdef int channels = min(resampled_seg.shape[1], output.shape[1])
    
    if output_start >= output_height or output_start < 0:
        return
    
    if output_end > output_height:
        output_end = output_height
    
    cdef int segment_length = output_end - output_start
    if segment_length <= 0:
        return
    
    # Apply the segment to output
    cdef int i, ch
    cdef DTYPE_t sample_val, env_val
    
    if apply_release_fade and release_env is not None:
        # With release envelope
        for i in range(segment_length):
            env_val = release_env[i] if i < release_env.shape[0] else 0.0
            for ch in range(channels):
                sample_val = resampled_seg[i, ch] * velocity_factor * env_val
                output[output_start + i, ch] += sample_val
    else:
        # Without release envelope
        for i in range(segment_length):
            for ch in range(channels):
                sample_val = resampled_seg[i, ch] * velocity_factor
                output[output_start + i, ch] += sample_val

@cython.boundscheck(False)
@cython.wraparound(False)
def create_release_envelope_fast(int length, double release_time, int sample_rate):
    """Fast release envelope creation"""
    if length <= 0:
        return np.array([], dtype=np.float32)
    
    cdef cnp.ndarray[DTYPE_t, ndim=1] envelope = np.zeros(length, dtype=np.float32)
    cdef double decay_factor = pow(0.001, 1.0 / (release_time * sample_rate))  # -60dB decay
    cdef int i
    cdef double env_val = 1.0
    
    for i in range(length):
        envelope[i] = env_val
        env_val *= decay_factor
    
    return envelope

@cython.boundscheck(False)
@cython.wraparound(False)
def find_relevant_pitch_bends_fast(
    list channel_bends,
    double note_start_time,
    double note_end_time
):
    """Fast pitch bend lookup"""
    cdef list relevant_bends = []
    cdef double bend_time, bend_value
    cdef double initial_bend = 0.0
    
    # Find initial bend value
    for bend_time, bend_value in channel_bends:
        if bend_time <= note_start_time:
            initial_bend = bend_value
        else:
            break
    
    # Add initial bend
    relevant_bends.append((note_start_time, initial_bend))
    
    # Find bends during note lifetime
    for bend_time, bend_value in channel_bends:
        if note_start_time < bend_time <= note_end_time:
            relevant_bends.append((bend_time, bend_value))
    
    # Add final bend if needed
    if len(relevant_bends) == 1 or relevant_bends[-1][0] < note_end_time:
        final_bend = relevant_bends[-1][1] if relevant_bends else 0.0
        relevant_bends.append((note_end_time, final_bend))
    
    return relevant_bends
