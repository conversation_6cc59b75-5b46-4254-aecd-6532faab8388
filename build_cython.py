#!/usr/bin/env python3
"""
Build script for Cython extensions.
This will compile the pitch bend processing functions for better performance.
"""

import subprocess
import sys
import os

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        import Cython
        print(f"✓ Cython {Cython.__version__} found")
    except ImportError:
        print("✗ Cython not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Cython"])
        print("✓ Cython installed")
    
    try:
        import numpy
        print(f"✓ NumPy {numpy.__version__} found")
    except ImportError:
        print("✗ NumPy not found. Please install NumPy first.")
        return False
    
    return True

def build_extensions():
    """Build the Cython extensions"""
    if not check_dependencies():
        return False
    
    print("\nBuilding Cython extensions...")
    
    try:
        # Build the pitch bend extension
        result = subprocess.run([
            sys.executable, "setup_cython.py", 
            "build_ext", "--inplace"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Cython extensions built successfully!")
            print("\nBuild output:")
            print(result.stdout)
            return True
        else:
            print("✗ Failed to build Cython extensions")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Error building extensions: {e}")
        return False

def test_extensions():
    """Test if the extensions work correctly"""
    print("\nTesting Cython extensions...")
    
    try:
        from pitch_bend_fast import resample_segment_fast, calculate_pitch_bend_fast
        import numpy as np
        
        # Test resample function
        test_data = np.random.rand(1000, 2).astype(np.float32)
        resampled = resample_segment_fast(test_data, 500)
        assert resampled.shape == (500, 2), f"Expected (500, 2), got {resampled.shape}"
        print("✓ Resample function works")
        
        # Test pitch bend calculation
        bend_result = calculate_pitch_bend_fast(12287, 2.0)
        expected = ((12287 - 8192) / 8192) * 2.0
        assert abs(bend_result - expected) < 0.001, f"Expected {expected}, got {bend_result}"
        print("✓ Pitch bend calculation works")
        
        print("🎉 All Cython extensions are working correctly!")
        return True
        
    except ImportError as e:
        print(f"✗ Could not import extensions: {e}")
        return False
    except Exception as e:
        print(f"✗ Extension test failed: {e}")
        return False

def main():
    """Main build process"""
    print("=== Cython Extension Builder ===")
    
    if not os.path.exists("pitch_bend_fast.pyx"):
        print("✗ pitch_bend_fast.pyx not found!")
        return False
    
    if not os.path.exists("setup_cython.py"):
        print("✗ setup_cython.py not found!")
        return False
    
    # Build extensions
    if not build_extensions():
        print("\n❌ Build failed!")
        return False
    
    # Test extensions
    if not test_extensions():
        print("\n❌ Tests failed!")
        return False
    
    print("\n🎉 SUCCESS! Cython extensions are ready to use.")
    print("The MIDI processing should now be significantly faster.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
