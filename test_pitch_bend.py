#!/usr/bin/env python3
"""
Test script to verify the new pitch bending system works correctly.
"""

import sys
import os
import numpy as np
from typing import Dict, <PERSON><PERSON>

# Add the current directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pitch_bend_parsing():
    """Test that pitch bend events are parsed correctly"""
    print("Testing pitch bend parsing...")
    
    # Import the main module
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("midi_converter", "midi-to-audio-with-correct-bpm - Copy (3) - Copy.py")
        midi_converter = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(midi_converter)
        
        # Test pitch bend event creation with raw 14-bit value
        event = midi_converter.MIDIEvent(100, 'pitch_bend', channel=0, pitch_bend=8192)  # Center position
        assert event.event_type == 'pitch_bend'
        assert event.channel == 0
        assert event.pitch_bend == 8192
        print("✓ Pitch bend event creation works")
        
        # Test CC event creation
        cc_event = midi_converter.MIDIEvent(200, 'control_change', channel=1, param1=101, param2=64)
        assert cc_event.event_type == 'control_change'
        assert cc_event.channel == 1
        assert cc_event.param1 == 101
        assert cc_event.param2 == 64
        print("✓ CC event creation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing pitch bend parsing: {e}")
        return False

def test_resample_segment():
    """Test the resampling function"""
    print("Testing resample segment function...")
    
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("midi_converter", "midi-to-audio-with-correct-bpm - Copy (3) - Copy.py")
        midi_converter = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(midi_converter)
        
        # Create a dummy process_track_chunk function to access resample_segment
        # Since it's defined inside the function, we'll test the concept
        
        # Test data: simple stereo sine wave
        sample_rate = 44100
        duration = 0.1  # 100ms
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        freq = 440  # A4
        
        # Create stereo test signal
        left_channel = np.sin(2 * np.pi * freq * t) * 16384
        right_channel = np.sin(2 * np.pi * freq * t * 1.1) * 16384  # Slightly different frequency
        test_signal = np.column_stack([left_channel, right_channel]).astype(np.int32)
        
        print(f"✓ Created test signal: {test_signal.shape} samples")
        
        # Test resampling to different lengths
        for factor in [0.5, 1.0, 2.0]:
            new_length = int(samples * factor)
            if new_length > 0:
                # Simulate the resample_segment function
                if len(test_signal) == 0:
                    resampled = np.zeros((new_length, test_signal.shape[1]), dtype=test_signal.dtype)
                else:
                    x_old = np.linspace(0, 1, num=len(test_signal))
                    x_new = np.linspace(0, 1, num=new_length)
                    resampled = []
                    for ch in range(test_signal.shape[1]):
                        resampled_channel = np.interp(x_new, x_old, test_signal[:, ch])
                        resampled.append(resampled_channel)
                    resampled = np.stack(resampled, axis=-1).astype(test_signal.dtype)
                
                print(f"✓ Resampling factor {factor}: {len(test_signal)} -> {len(resampled)} samples")
                assert resampled.shape[1] == 2  # Should maintain stereo
                assert len(resampled) == new_length
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing resample segment: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pitch_bend_calculation():
    """Test pitch bend semitone calculations"""
    print("Testing pitch bend calculations...")
    
    try:
        # Test the pitch bend range calculations using the exact same formula as midi loader.py
        test_cases = [
            # (bend_value_14bit, range_semitones, expected_semitones)
            (8192, 2.0, 0.0),      # Center position (no bend)
            (16383, 2.0, 2.0),     # Maximum up bend, 2 semitone range
            (0, 2.0, -2.0),        # Maximum down bend, 2 semitone range
            (12287, 2.0, 1.0),     # Half up bend (8192 + 4095), 2 semitone range
            (16383, 12.0, 12.0),   # Maximum up bend, 12 semitone range (octave)
            (4096, 2.0, -1.0),     # Half down bend (8192 - 4096), 2 semitone range
        ]

        for bend_value, range_semitones, expected in test_cases:
            # Use exact same calculation as midi loader.py
            calculated = ((bend_value - 8192) / 8192) * range_semitones
            assert abs(calculated - expected) < 0.01, f"Expected {expected}, got {calculated}"
            print(f"✓ Bend value {bend_value} with range {range_semitones} = {calculated:.3f} semitones")
        
        # Test pitch factor calculations
        for semitones in [-12, -2, 0, 2, 12]:
            pitch_factor = 2 ** (semitones / 12.0)
            print(f"✓ {semitones} semitones = pitch factor {pitch_factor:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing pitch bend calculations: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing the new pitch bending system...")
    print("=" * 50)
    
    tests = [
        test_pitch_bend_parsing,
        test_resample_segment,
        test_pitch_bend_calculation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
            print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The pitch bending system should work correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
